should = require('should')
helpers = require('../00_common/helpers')
request = null
describe 'stigma api test', ->
  before (done) ->
    @timeout(30000)
    request = helpers.getServer()
    dbs = [
      { db: 'vow', table: 'stigmatized' }
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, () ->
        helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie1) ->
          should.not.exists err
          cookie1.push 'apsv=appDebug' if cookie1
          helpers.userCookies.user = cookie1
          helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie2) ->
            should.not.exists err
            cookie2.push 'apsv=appDebug' if cookie2
            helpers.userCookies.user1 = cookie2
            helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie3) ->
              should.not.exists err
              cookie3.push 'apsv=appDebug' if cookie3
              helpers.userCookies.admin = cookie3
              setTimeout(done, 2500)
    return

  describe 'user add stigma prop and admin approve', ->
    it 'add stigma prop and approve', (done) ->
      request
        .post('1.5/stigma/update')
        .set('Accept', 'application/json')
        .set('Cookie', ['apsv=appDebug'])
        .set('Cookie', helpers.userCookies.user)
        .expect('Content-Type', /json/)
        .send({
          addr: '574 Millwood Road'
          city: 'Toronto'
          cnty: 'Canada'
          dt: '2021-10-01'
          lat: 43.7036946
          lng: -79.3783671
          m: 'The room is full of marijuana'
          prov: 'Ontario'
          ptp: 'Apartment'
          ref1: 'https://www.baidu.com/'
          ref2: ''
          title: 'The drug in the house'
          type: 'Drug'
          zip: 'M4S 1K5'
          })
        .end (err, ret) ->
          # console.log '+++++check status',ret.body
          ret.body.ok.should.be.exactly(1)
          ret.body.msg.should.be.exactly('Saved')
          # console.log '+++++add user id',test.setpost
          request
            .post('1.5/stigma/list')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies.user)
            .expect('Content-Type', /json/)
            .send({
              page: 0
              sort: 'mt'
              status: 'toreview'
            })
            .end (err, list) ->
              # console.log '++++list',list.body
              list.body.data.length.should.be.above(0)
              for element in list.body.data
                if element.dt is '2021-10-01'
                  found = element
              request
                .post('1.5/stigma/approve')
                .set('Accept', 'application/json')
                .set('Cookie', ['apsv=appDebug'])
                .set('Cookie', helpers.userCookies.admin)
                .expect('Content-Type', /json/)
                .send({
                  id:found._id
                })
                .end (err, approve) ->
                  # console.log '++++get ta list',JSON.stringify approve.body
                  approve.body.ok.should.be.exactly(1)
                  approve.body.msg.should.be.exactly('Approved')
                  done()
              return
          return
      return

  describe 'edit stigma prop and delete', ->
    it 'edit stigma prop and delete', (done) ->
      request
        .post('1.5/stigma/list')
        .set('Accept', 'application/json')
        .set('Cookie', ['apsv=appDebug'])
        .set('Cookie', helpers.userCookies.admin)
        .expect('Content-Type', /json/)
        .send({
          page: 0
          sort: 'dt'
          status: 'approved'
        })
        .end (err, list) ->
          # console.log '++++list',list.body
          list.body.data.length.should.be.above(0)
          edit = list.body.data[0]
          edit.ptp = 'House'
          # console.log edit
          request
            .post('1.5/stigma/update')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies.admin)
            .expect('Content-Type', /json/)
            .send(edit)
            .end (err, edited) ->
              edited.body.ok.should.be.exactly(1)
              edited.body.msg.should.be.exactly('Saved')
              request
                .post('1.5/stigma/detail')
                .set('Accept', 'application/json')
                .set('Cookie', ['apsv=appDebug'])
                .set('Cookie', helpers.userCookies.admin)
                .expect('Content-Type', /json/)
                .send({
                  id: edit._id
                })
                .end (err, stigma) ->
                  # console.log '++++stigma',stigma.body
                  stigma.body.data.ptp.should.be.exactly('House')
                  request
                    .post('1.5/stigma/delete')
                    .set('Accept', 'application/json')
                    .set('Cookie', ['apsv=appDebug'])
                    .set('Cookie', helpers.userCookies.admin)
                    .expect('Content-Type', /json/)
                    .send({
                      id: edit._id
                    })
                    .end (err, delected) ->
                      delected.body.ok.should.be.exactly(1)
                      delected.body.msg.should.be.exactly('Removed')
                      done()
                  return
              return
          return
      return

  describe 'get stigma in map', ->
    tests = [
      {
        desc: 'should get stigma success in bbox'
        post: {
          bbox: [-79.41778197673918, 43.68365377823227, -79.37636802325298, 43.7268447718414]
        }
        expected: { 'ok': 1 }
      },
      {
        desc: 'should get stigma success in ne,sw'
        post: {
          ne: [43.72688467518407, -79.37636802323613]
          sw: [43.68361384609722, -79.41778197672417]
          }
        expected: { 'ok': 1 }
      },
      {
        desc: 'should get [] without geo '
        post: {
          }
        expected: { 'ok': 0 }
      },
    ]
    tests.forEach (test) ->
      it test.desc, (done) ->
        request
          .post('stigma/house')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', helpers.userCookies.user)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, list) ->
            # console.log '++++list',list.body
            if test.expected.ok is 1
              list.body.cnt.should.be.above(0)
              list.body.items.length.should.be.above(0)
            else
              list.body.cnt.should.be.exactly(0)
              list.body.items.length.should.be.exactly(0)
            done()
        return
  # did not find vue to invoke this api
  # describe 'shared stigma', ->
  #   tests = [
  #     {
  #       desc: ' successed'
  #       post: {}
  #       expected: { 'ok': 1 }
  #     },
  #     {
  #       desc: 'has param pre'
  #       post: {pre:1}
  #       expected: { 'ok': 0 }
  #     },
  #   ]
  #   tests.forEach (test) ->
  #     it test.desc, (done) ->
  #       request
  #         .post('stigma/house/shared')
  #         .set('Accept', 'application/json')
  #         .set('Cookie', ['apsv=appDebug'])
  #         .set('Cookie', helpers.userCookies.user)
  #         .expect('Content-Type', /json/)
  #         .send(test.post)
  #         .end (err, res) ->
  #           # console.log '++++list',list.body
  #           for k,v of test.expected
  #             res.body[k].should.be.exactly(v)
  #           done()
  #       return

  describe 'error condition', ->
    tests = [
      {
        desc: 'update Access Denied'
        url:'1.5/stigma/update'
        post: {
          _id:1903
          addr: '574 Millwood Road'
          city: 'Toronto'
          cnty: 'Canada'
          dt: '2021-10-01'
          lat: 43.7036946
          lng: -79.3783671
          m: 'The room is full of marijuana'
          prov: 'Ontario'
          ptp: 'Apartment'
          ref1: 'https://www.baidu.com/'
          ref2: ''
          title: 'The drug in the house'
          type: 'Drug'
          zip: 'M4S 1K5'
        }
        expected: { e:  'Access Denied' }
      },
      {
        desc: 'update bad parameter'
        url:'1.5/stigma/update'
        post: null
        expected: { e:'Bad Parameter' }
      },
      {
        desc: 'detail bad parameter'
        url:'1.5/stigma/detail'
        post: {}
        expected: { e:'Bad Parameter' }
      },
      {
        desc: 'list bad parameter'
        url:'1.5/stigma/list'
        post: {}
        expected: { e:'Bad Parameter' }
      },
      {
        desc: 'delete Access Denied'
        url:'1.5/stigma/delete'
        post: {
          id:1903
        }
        expected: { e:  'Access Denied' }
      },
      {
        desc: 'delete bad parameter'
        url:'1.5/stigma/delete'
        post: null
        expected: { e:'Bad Parameter' }
      },
      {
        desc: 'approve Access Denied'
        url:'1.5/stigma/approve'
        post: {
          id:1903
        }
        expected: { e:  'Access Denied' }
      },
      {
        desc: 'approve bad parameter'
        url:'1.5/stigma/approve'
        post: null
        expected: { e:'Bad Parameter' }
      },
      {
        desc: 'approve bad parameter'
        url:'1.5/stigma/detail'
        post: {id:190}
        expected: {  }
      },
    ]
    tests.forEach (test) ->
      it test.desc, (done) ->
        request
          .post(test.url)
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', helpers.userCookies.user)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res) ->
            # console.log '++++res',res.body
            for k,v of test.expected
              res.body[k].should.be.exactly(v)
            done()
        return

  describe 'test get request', ->
    tests=[
      {
        desc:'test get stigmatized list'
        url:'1.5/stigma/list'
      },
      {
        desc:'test get stigmatized edit'
        url:'1.5/stigma/edit'
        # fail:1
      },
      {
        desc:'test get stigmatized edit whit id'
        url:'1.5/stigma/edit?id=1903'
        fail:1
      },
      {
        desc:'test get stigmatized detail'
        url:'1.5/stigma/detail'
      },
      {
        desc:'test get stigma/house'
        url:'stigma/house?lat=43.7052296823361&lng=-79.3998191054558'
        fail:1
      },
      {
        desc:'test get stigma/house'
        url:'stigma/house?loc=43.7052296823361,-79.3998191054558'
        fail:1
      },
    ]
    tests.forEach (test,i)->
      it "#{test.desc}", (done)->
        @timeout(300000)
        request
          .get(test.url)
          .set('Accept', 'application/json')
          .set('Cookie', helpers.userCookies.user)
          .expect('Content-Type', /json/)
          .end (err, res)->
            # console.log res
            if test.fail
              res.res.statusCode.should.be.equal(302)
            else
              res.res.statusCode.should.be.equal(200)
            done()
        return