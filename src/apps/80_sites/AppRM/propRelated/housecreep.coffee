# limiter = INCLUDE 'lib.limiter'
# conf = CONFIG()
# Stigmatized = COLLECTION 'vow','stigmatized'

# StigmatizedCol = COLLECTION 'vow','stigmatized'
SysNotifyModel = MODEL 'SysNotify'
UserModel = MODEL 'User'
Stigmatized = MODEL 'Stigmatized'
mapServer =  INCLUDE 'lib.mapServer'
helpersStr = INCLUDE 'lib.helpers_string'
{respError} = INCLUDE 'libapp.responseHelper'
debug = DEBUG()

APP 'stigma/house'

DEFAULT_LIMIT = 15
MAX_LIMIT = 150


ADDSTDFN 'getStigma',(opt,cb)->
  q = opt
  Stigmatized.getListByBbox q,(err,ret)->
    return cb err,null if err
    cb null,ret

POST (req,resp)->
  unless req.getDevType() is 'app'
    debug.error "Not App Access creepyHouse: #{req.remoteIP()}"
    return resp.send {err:1}
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    q = req.body
    Stigmatized.getListByBbox q,(err,ret)->
      return resp.send err if err
      resp.send ret

POST 'shared',(req,resp)->
  if req.param 'pre'
    return resp.send {ok:0, preshare:1}
  UserModel.appAuth {req,resp,userfields:['eml','nm_zh','nm_en','nm']},(user)->
    return resp.send {ok:0} unless user
    UserModel.setTskDn user._id, {tskDn:'creepyHouse'},(err)->
      if err
        debug.error err
      debug.warn "#{user._id} #{user.eml} #{user.nm_zh or user.nm_en} #{user.nm} #{user.roles} shared creepyHouse."
      # SysNotifyModel.notifyAdmin "Shared creepyHouse. #{req.remoteIP()} User:#{user._id}:#{user.eml} #{user.nm_zh or user.nm_en} #{user.nm} #{user.roles}"
      resp.send {ok:1,j:'/stigma/house'}

GET (req,resp)->
  loc = req.param 'loc'
  lat = req.param 'lat'
  lng = req.param 'lng'
  gps = req.param 'gps' # use gps to locate me
  cityName = req.param 'cityName'
  # return if req.redirectHTTPWhenCnDitu()
  unless isApp = req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  loginUrl = '/1.5/user/login'
  if req.query.referer is 'nativeAutocomplete'
    #handle goback on native
    loginUrl += '#index'
  UserModel.appAuth {req,resp,url:loginUrl},(user)->
    UserModel.findByTskDnAndId user._id,'creepyHouse',(err,found)->
      if err then debug.error err
      #unless found
      #  return resp.redirect "/event?tpl=creepyHouse&u=/stigma/house/shared"
      if found
        req.session.set "rhLimit",MAX_LIMIT,->
      # mapboxkey = mapServer.getMapboxAPIKey {isApp}
      # resp.ckup "map",{},'_',{noAngular:true,mapboxkey:mapboxkey}
      url = '/1.5/map/webMap?ss=1&zoom=15&tab=stigma'
      if loc
        lat = loc[0]
        lng = loc[1]
      lng = helpersStr.formatLng lng
      if (lat and (not helpersStr.isLatitude lat)) or (lng and (not helpersStr.isLongitude lng))
        return resp.ckup 'generalError', {err_tran:req.l10n('Location information is wrong')}
      url = url+ "&lat=#{lat}&lng=#{lng}" if loc
      return resp.redirect url

Object.assign Stigmatized.STIGMATIZED_EXTENDED_FILES,Stigmatized.STIGMATIZED_BASIC_FILES
ADDSTDFN 'findStigmatized',{needLogin:true},(opt,cb)->
  Stigmatized.getListByBbox opt,cb

ADDSTDFN 'getStigmatized',{needLogin:true},(opt,cb)->
  q = {_id:opt.id}
  Stigmatized.findOneById opt.id,{fields:Stigmatized.STIGMATIZED_EXTENDED_FILES},(err,ret)->
    if err
      debug.error err
      return cb MSG_STRINGS.DB_ERROR
    cb null, ret

APP '1.5'
APP 'stigma',true


POST 'update',(req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless body = req.body
  if (body._id or body.id) and not req.isAllowed 'stigmaAdmin'
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
  UserModel.appAuth {req,resp,userfields:['eml','nm','nm_zh','nm_en']},(user)->
    Stigmatized.createOrUpdate {body,user,isAdmin:req.isAllowed 'stigmaAdmin'},(err)->
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1,msg:req.l10n 'Saved'}

# /1.5/stigma/edit
GET 'edit', (req, resp) ->
  UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->
    id = req.param 'id'
    if id and (not req.isAllowed 'stigmaAdmin')
      return resp.redirect '/1.5/stigma/list?tp=approved'
    resp.ckup 'stigmatized/edit', { id }


tabs = [
  { url: '/1.5/stigma/list?tp=approved', nm: 'Approved', selected: 'approved' },
  { url: '/1.5/stigma/list?tp=toreview', nm: 'Wait To Review', selected: 'toreview' }
]


# /1.5/stigma/detail?id=
GET 'detail', (req, resp) ->
  UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->
    resp.ckup 'stigmatized/detail', {}

# /1.5/stigma/detail?id=
POST 'detail',(req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?.id
  UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->
    Stigmatized.findOneById {id},(err,detail)->
      rreturn respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} if err
      resp.send {ok:1,data:detail}

# /1.5/stigma/list?tp=approved/toreview
GET 'list', (req, resp) ->
  UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->
    opt = {
      tabs:tabs
      d: req.param('d')
      id:req.param 'tp' or 'toreview'
      }
    resp.ckup 'stigmatized/list', opt

# /1.5/stigma/list
POST 'list',(req,resp)->
  body = req.body
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless status = body.status
  UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->
    if status is 'approved'
      status = 'A'
    else
      status = 'U'
    isAdmin = req.isAllowed 'stigmaAdmin'
    Stigmatized.getList user._id,{status,req,isAdmin,sort:body.sort,page:body.page},(err,list)->
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1,data:list}

# /1.5/stigma/delete?id=
POST 'delete',(req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?.id
  return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} if not req.isAllowed 'stigmaAdmin'
  UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->
    Stigmatized.deleteById id,{uid:user._id},(err)->
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1,msg:req.l10n 'Removed'}

# /1.5/stigma/approve?id=
POST 'approve',(req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?.id
  return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} if not req.isAllowed 'stigmaAdmin'
  UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->
    Stigmatized.approve id,(err)->
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1,msg:req.l10n 'Approved'}